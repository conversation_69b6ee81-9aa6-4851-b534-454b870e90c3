<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select Ref Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-field {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        .error {
            color: red;
            font-size: 14px;
            margin-top: 5px;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #005a87;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .info {
            background: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #007cba;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Select Component Ref Testing Guide</h1>
    
    <div class="info">
        <h3>🎯 What We're Testing</h3>
        <p>This page demonstrates how to test that Select components with <code>ref={field.ref}</code> properly:</p>
        <ul>
            <li><strong>Auto-scroll to validation errors</strong> - Form should scroll to the first invalid Select</li>
            <li><strong>Programmatic focus</strong> - Select should receive focus when called via JavaScript</li>
            <li><strong>Accessibility</strong> - Screen readers should properly announce validation states</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test 1: Auto-scroll on Validation Error</h2>
        <p>This simulates a long form where a Select field at the bottom has a validation error.</p>
        
        <form id="testForm">
            <!-- Add some spacing to make scrolling visible -->
            <div style="height: 800px; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center; margin: 20px 0;">
                <p style="text-align: center; color: #666;">
                    📜 Long form content...<br>
                    Scroll down to see the Select field
                </p>
            </div>
            
            <div class="form-field">
                <label for="testSelect">Required Select Field *</label>
                <select id="testSelect" required>
                    <option value="">-- Please select an option --</option>
                    <option value="option1">Option 1</option>
                    <option value="option2">Option 2</option>
                    <option value="option3">Option 3</option>
                </select>
                <div class="error" id="selectError" style="display: none;">This field is required</div>
            </div>
            
            <button type="submit">Submit Form</button>
        </form>
        
        <div id="testResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Programmatic Focus</h2>
        <p>Test that the Select component can receive focus programmatically.</p>
        
        <div class="form-field">
            <label for="focusSelect">Focus Test Select</label>
            <select id="focusSelect">
                <option value="">-- Select an option --</option>
                <option value="focus1">Focus Option 1</option>
                <option value="focus2">Focus Option 2</option>
            </select>
        </div>
        
        <button onclick="focusSelect()">Focus the Select</button>
        <button onclick="blurSelect()">Blur the Select</button>
        
        <div id="focusResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Accessibility Attributes</h2>
        <p>Check that aria-invalid and other accessibility attributes are properly set.</p>
        
        <div class="form-field">
            <label for="ariaSelect">Accessibility Test Select</label>
            <select id="ariaSelect" aria-describedby="ariaHelp ariaError">
                <option value="">-- Select an option --</option>
                <option value="aria1">Aria Option 1</option>
                <option value="aria2">Aria Option 2</option>
            </select>
            <div id="ariaHelp">This field demonstrates accessibility attributes</div>
            <div id="ariaError" class="error" style="display: none;">This field has an error</div>
        </div>
        
        <button onclick="toggleAriaInvalid()">Toggle aria-invalid</button>
        <button onclick="checkAriaAttributes()">Check Aria Attributes</button>
        
        <div id="ariaResult"></div>
    </div>

    <script>
        // Test 1: Form validation and auto-scroll
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const select = document.getElementById('testSelect');
            const error = document.getElementById('selectError');
            const result = document.getElementById('testResult');
            
            if (!select.value) {
                error.style.display = 'block';
                select.style.borderColor = 'red';
                
                // Simulate what React Hook Form does with ref
                select.scrollIntoView({ behavior: 'smooth', block: 'center' });
                select.focus();
                
                result.innerHTML = '<div class="error">❌ Validation failed - Select should be scrolled into view and focused</div>';
            } else {
                error.style.display = 'none';
                select.style.borderColor = '#ccc';
                result.innerHTML = '<div class="success">✅ Form submitted successfully!</div>';
            }
        });

        // Test 2: Programmatic focus
        function focusSelect() {
            const select = document.getElementById('focusSelect');
            const result = document.getElementById('focusResult');
            
            select.focus();
            result.innerHTML = '<div class="success">✅ Select focused programmatically</div>';
            
            // Check if focus was successful
            setTimeout(() => {
                if (document.activeElement === select) {
                    result.innerHTML += '<div class="success">✅ Focus confirmed - Select is the active element</div>';
                } else {
                    result.innerHTML += '<div class="error">❌ Focus failed - Select is not the active element</div>';
                }
            }, 100);
        }

        function blurSelect() {
            const select = document.getElementById('focusSelect');
            const result = document.getElementById('focusResult');
            
            select.blur();
            result.innerHTML = '<div>ℹ️ Select blurred</div>';
        }

        // Test 3: Accessibility attributes
        let ariaInvalid = false;
        
        function toggleAriaInvalid() {
            const select = document.getElementById('ariaSelect');
            const error = document.getElementById('ariaError');
            const result = document.getElementById('ariaResult');
            
            ariaInvalid = !ariaInvalid;
            
            if (ariaInvalid) {
                select.setAttribute('aria-invalid', 'true');
                select.style.borderColor = 'red';
                error.style.display = 'block';
                result.innerHTML = '<div class="error">❌ aria-invalid set to true</div>';
            } else {
                select.setAttribute('aria-invalid', 'false');
                select.style.borderColor = '#ccc';
                error.style.display = 'none';
                result.innerHTML = '<div class="success">✅ aria-invalid set to false</div>';
            }
        }

        function checkAriaAttributes() {
            const select = document.getElementById('ariaSelect');
            const result = document.getElementById('ariaResult');
            
            const attributes = {
                'aria-invalid': select.getAttribute('aria-invalid'),
                'aria-describedby': select.getAttribute('aria-describedby'),
                'id': select.getAttribute('id')
            };
            
            let output = '<h4>Current Aria Attributes:</h4><ul>';
            for (const [attr, value] of Object.entries(attributes)) {
                output += `<li><strong>${attr}:</strong> ${value || 'not set'}</li>`;
            }
            output += '</ul>';
            
            result.innerHTML = output;
        }
    </script>
</body>
</html>
